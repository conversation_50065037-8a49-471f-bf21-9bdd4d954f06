# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Codebase Overview

This repository contains a single Python file that implements an Excel-like application with AI integration capabilities. The main components are:

1. OllamaClient - Client for communicating with Ollama AI models
2. QwenClient - Client for communicating with Qwen AI models
3. ExcelSimpleApp - Main GUI application class implementing spreadsheet functionality with AI features

The application uses tkinter for the GUI, pandas for data handling, and matplotlib for data visualization.

## Development Commands

Since this is a Python application, the main command is:
```bash
python local_ai_fixedV0.85.py
```

## Architecture

The codebase follows a single-file structure with these main components:

1. AI Client Classes:
   - OllamaClient handles communication with Ollama models
   - QwenClient handles communication with Qwen models
   
2. Main Application Class:
   - ExcelSimpleApp implements the GUI and core spreadsheet functionality
   - Integrates AI features into spreadsheet operations
   - Manages data visualization with matplotlib
   - Handles file operations for Excel files

The application is designed to run as a standalone desktop application with integrated AI capabilities for data analysis.